#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试班级查询bug修复效果
"""

import requests
import json

BASE_URL = 'http://127.0.0.1:5000'

# 创建会话以保持登录状态
session = requests.Session()

def login_as_admin():
    """以管理员身份登录"""
    try:
        # 尝试登录
        login_data = {
            'username': 'admin',
            'password': 'admin123'
        }

        response = session.post(f'{BASE_URL}/auth/login', data=login_data)

        # 检查是否登录成功
        if response.status_code == 302 or response.url.endswith('/'):
            print("✅ 管理员登录成功")
            return True
        else:
            print(f"❌ 登录失败: {response.status_code}")
            return False

    except Exception as e:
        print(f"❌ 登录异常: {e}")
        return False

def test_bug_fix():
    """测试bug修复效果"""
    print("=" * 60)
    print("测试班级查询bug修复效果")
    print("=" * 60)
    
    try:
        # 步骤1: 测试正常查询（物联网专业）
        print("\n步骤1: 测试正常查询（物联网专业）")
        print("-" * 40)
        params1 = {
            'academic_year': '2024-2025',
            'semester': 1,
            'major': '物联网'
        }
        
        response1 = session.get(f'{BASE_URL}/analysis/api/class/data', params=params1)
        print(f"请求URL: {response1.url}")
        print(f"响应状态: {response1.status_code}")
        
        if response1.status_code == 200:
            data1 = response1.json()
            if data1.get('success'):
                class_count1 = len(data1.get('data', []))
                print(f"✅ 返回 {class_count1} 个班级")
                if class_count1 > 0:
                    for i, cls in enumerate(data1['data'][:3]):
                        print(f"  班级 {i+1}: {cls['class_name']} (年级: {cls['grade']}) - {cls['student_count']}人")
            else:
                print(f"❌ 错误: {data1.get('error')}")
                return False
        else:
            print(f"❌ 请求失败: {response1.status_code}")
            return False
        
        # 步骤2: 添加年级筛选条件（grade=2024）
        print("\n步骤2: 添加年级筛选条件（grade=2024）")
        print("-" * 40)
        params2 = {
            'academic_year': '2024-2025',
            'semester': 1,
            'major': '物联网',
            'grade': 2024
        }
        
        response2 = session.get(f'{BASE_URL}/analysis/api/class/data', params=params2)
        print(f"请求URL: {response2.url}")
        print(f"响应状态: {response2.status_code}")
        
        if response2.status_code == 200:
            data2 = response2.json()
            if data2.get('success'):
                class_count2 = len(data2.get('data', []))
                print(f"返回 {class_count2} 个班级")
                if class_count2 == 0:
                    print("⚠️  未找到2024年级的物联网班级（这可能是正常的）")
                else:
                    for i, cls in enumerate(data2['data'][:3]):
                        print(f"  班级 {i+1}: {cls['class_name']} (年级: {cls['grade']}) - {cls['student_count']}人")
            else:
                print(f"❌ 错误: {data2.get('error')}")
                return False
        else:
            print(f"❌ 请求失败: {response2.status_code}")
            return False
        
        # 步骤3: 移除年级筛选条件，回到原始查询
        print("\n步骤3: 移除年级筛选条件，回到原始查询")
        print("-" * 40)
        params3 = {
            'academic_year': '2024-2025',
            'semester': 1,
            'major': '物联网'
        }
        
        response3 = session.get(f'{BASE_URL}/analysis/api/class/data', params=params3)
        print(f"请求URL: {response3.url}")
        print(f"响应状态: {response3.status_code}")
        
        if response3.status_code == 200:
            data3 = response3.json()
            if data3.get('success'):
                class_count3 = len(data3.get('data', []))
                print(f"返回 {class_count3} 个班级")
                if class_count3 == 0:
                    print("❌ Bug仍然存在：移除筛选条件后仍然无法查询到数据")
                    return False
                else:
                    print("✅ 查询恢复正常")
                    for i, cls in enumerate(data3['data'][:3]):
                        print(f"  班级 {i+1}: {cls['class_name']} (年级: {cls['grade']}) - {cls['student_count']}人")
                    return True
            else:
                print(f"❌ 错误: {data3.get('error')}")
                return False
        else:
            print(f"❌ 请求失败: {response3.status_code}")
            return False
        
    except Exception as e:
        print(f"❌ 测试异常: {e}")
        return False

def test_ranking_api():
    """测试排行榜API（用于班级详情）"""
    print("\n步骤4: 测试排行榜API（班级详情数据源）")
    print("-" * 40)
    
    try:
        params = {
            'academic_year': '2024-2025',
            'semester': 1,
            'major': '物联网'
        }
        
        response = session.get(f'{BASE_URL}/ranking/api/data', params=params)
        print(f"请求URL: {response.url}")
        print(f"响应状态: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            if data.get('success'):
                student_count = len(data.get('data', []))
                print(f"✅ 返回 {student_count} 个学生记录")
                
                # 统计班级分布
                class_distribution = {}
                for student in data['data']:
                    class_name = student.get('class_name', '未知')
                    if class_name not in class_distribution:
                        class_distribution[class_name] = 0
                    class_distribution[class_name] += 1
                
                print("班级分布:")
                for class_name, count in sorted(class_distribution.items()):
                    print(f"  {class_name}: {count}人")
                
                return True
            else:
                print(f"❌ 错误: {data.get('error')}")
                return False
        else:
            print(f"❌ 请求失败: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ 测试异常: {e}")
        return False

def main():
    """主函数"""
    if not login_as_admin():
        print("❌ 登录失败，无法继续测试")
        return
    
    tests = [
        ("班级查询bug修复测试", test_bug_fix),
        ("排行榜API测试", test_ranking_api)
    ]
    
    results = []
    for test_name, test_func in tests:
        print(f"\n🧪 {test_name}")
        result = test_func()
        results.append((test_name, result))
    
    print("\n" + "=" * 60)
    print("测试结果汇总")
    print("=" * 60)
    
    passed = 0
    for test_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\n总计: {passed}/{len(results)} 个测试通过")
    
    if passed == len(results):
        print("🎉 所有测试通过！Bug修复成功！")
    else:
        print("⚠️  部分测试失败，需要进一步调试")

if __name__ == '__main__':
    main()
