{% extends "base.html" %}

{% block title %}班级分析 - 学生学业数据管理与分析平台{% endblock %}

{% block extra_head %}
<!-- Marked.js for Markdown rendering -->
<script src="https://cdn.jsdelivr.net/npm/marked/marked.min.js"></script>
<!-- Highlight.js for code syntax highlighting -->
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/highlight.js/11.9.0/styles/github.min.css">
<script src="https://cdnjs.cloudflare.com/ajax/libs/highlight.js/11.9.0/highlight.min.js"></script>

<style>
.class-card {
    transition: transform 0.2s ease-in-out, box-shadow 0.2s ease-in-out;
    cursor: pointer;
}

.class-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(0,0,0,0.1);
}

.class-card.selected {
    border: 2px solid #4facfe;
    background: linear-gradient(135deg, #f8f9ff 0%, #e8f2ff 100%);
}

.ai-analysis {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 15px;
    padding: 0;
    box-shadow: 0 10px 30px rgba(0,0,0,0.1);
    margin-top: 20px;
}

.ai-analysis .card-header {
    background: transparent;
    border-bottom: 1px solid rgba(255,255,255,0.2);
    color: white;
}

.ai-analysis .card-body {
    background: white;
    border-radius: 0 0 15px 15px;
    padding: 25px;
}

.ai-content {
    line-height: 1.8;
    font-size: 14px;
}

.ai-content h1, .ai-content h2, .ai-content h3 {
    color: #2c3e50;
    margin-top: 20px;
    margin-bottom: 10px;
}

.ai-content ul, .ai-content ol {
    margin-left: 20px;
    margin-bottom: 15px;
}

.ai-content li {
    margin-bottom: 5px;
}

.ai-content code {
    background: #f8f9fa;
    padding: 2px 6px;
    border-radius: 4px;
    font-family: 'Courier New', monospace;
}

.ai-content pre {
    background: #f8f9fa;
    padding: 15px;
    border-radius: 8px;
    overflow-x: auto;
    margin: 15px 0;
}

.ai-content blockquote {
    border-left: 4px solid #4facfe;
    padding-left: 15px;
    margin: 15px 0;
    color: #666;
    font-style: italic;
}

.stats-card {
    background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
    color: white;
    border: none;
}

.stats-card .card-body {
    padding: 1.5rem;
}

.stats-number {
    font-size: 2rem;
    font-weight: bold;
    margin-bottom: 0.5rem;
}

.stats-label {
    font-size: 0.9rem;
    opacity: 0.9;
}
</style>
{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <h1 class="h3 mb-4">
            <i class="fas fa-chart-bar me-2"></i>
            班级分析
        </h1>
    </div>
</div>

<!-- 筛选条件 -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-filter me-2"></i>
                    筛选条件
                </h5>
            </div>
            <div class="card-body">
                <form id="filterForm" class="row g-3">
                    <div class="col-md-3">
                        <label for="academicYear" class="form-label">学年 <span class="text-danger">*</span></label>
                        <select class="form-select" id="academicYear" name="academic_year" required>
                            <option value="">请选择学年</option>
                            {% for semester in filters.semesters %}
                                <option value="{{ semester.academic_year }}">{{ semester.academic_year }}</option>
                            {% endfor %}
                        </select>
                    </div>
                    
                    <div class="col-md-3">
                        <label for="semester" class="form-label">学期 <span class="text-danger">*</span></label>
                        <select class="form-select" id="semester" name="semester" required>
                            <option value="">请选择学期</option>
                            <option value="1">第一学期</option>
                            <option value="2">第二学期</option>
                        </select>
                    </div>
                    
                    <div class="col-md-2">
                        <label for="major" class="form-label">专业</label>
                        <select class="form-select" id="major" name="major">
                            <option value="">全部专业</option>
                            {% if filters and filters.majors %}
                                {% for major in filters.majors %}
                                    <option value="{{ major.major_name }}">{{ major.major_name }}</option>
                                {% endfor %}
                            {% else %}
                                <!-- 调试信息 -->
                                <option disabled>无专业数据 (filters: {{ filters }})</option>
                            {% endif %}
                        </select>
                    </div>

                    <div class="col-md-2">
                        <label for="grade" class="form-label">年级</label>
                        <select class="form-select" id="grade" name="grade">
                            <option value="">全部年级</option>
                            {% if filters and filters.grades %}
                                {% for grade in filters.grades %}
                                    <option value="{{ grade }}">{{ grade }}级</option>
                                {% endfor %}
                            {% else %}
                                <!-- 动态加载年级选项 -->
                            {% endif %}
                        </select>
                    </div>

                    <div class="col-md-2">
                        <label class="form-label">&nbsp;</label>
                        <div class="d-grid gap-2 d-md-flex">
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-search me-1"></i>查询
                            </button>
                            <button type="button" class="btn btn-outline-secondary" id="resetBtn">
                                <i class="fas fa-undo me-1"></i>重置
                            </button>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<!-- 加载状态 -->
<div id="loadingSpinner" class="text-center" style="display: none;">
    <div class="spinner-border text-primary" role="status">
        <span class="visually-hidden">加载中...</span>
    </div>
    <p class="mt-2">正在加载班级数据...</p>
</div>

<!-- 班级概览统计 -->
<div id="classOverview" style="display: none;">
    <!-- 统计卡片 -->
    <div class="row mb-4" id="statsCards">
        <!-- 统计卡片将通过JavaScript动态生成 -->
    </div>
    
    <!-- 班级对比图表 -->
    <div class="row mb-4">
        <div class="col-md-6">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-chart-bar me-2"></i>
                        班级平均分对比
                    </h5>
                </div>
                <div class="card-body">
                    <div id="classComparisonChart" style="height: 400px;"></div>
                </div>
            </div>
        </div>
        
        <div class="col-md-6">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-chart-pie me-2"></i>
                        成绩分布情况
                    </h5>
                </div>
                <div class="card-body">
                    <div id="scoreDistributionChart" style="height: 400px;"></div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- 班级列表 -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-list me-2"></i>
                        班级详细信息
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row" id="classCards">
                        <!-- 班级卡片将通过JavaScript动态生成 -->
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 班级详情分析 -->
<div id="classDetailAnalysis" style="display: none;">
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-users me-2"></i>
                        <span id="selectedClassName">班级</span> 详细分析
                    </h5>
                    <div>
                        <button type="button" class="btn btn-success me-2" id="aiAnalysisBtn">
                            <i class="fas fa-robot me-1"></i>AI智能分析
                        </button>
                        <button type="button" class="btn btn-outline-secondary" id="backToOverviewBtn">
                            <i class="fas fa-arrow-left me-1"></i>返回概览
                        </button>
                    </div>
                </div>
                <div class="card-body">
                    <!-- 班级基本统计 -->
                    <div class="row mb-4" id="classDetailStats">
                        <!-- 统计信息将通过JavaScript动态生成 -->
                    </div>

                    <!-- 班级历年成绩趋势图 -->
                    <div class="row mb-4">
                        <div class="col-12">
                            <div class="card">
                                <div class="card-header">
                                    <h6 class="card-title mb-0">
                                        <i class="fas fa-chart-line me-2"></i>
                                        班级历年成绩趋势
                                    </h6>
                                </div>
                                <div class="card-body">
                                    <div id="classTrendChart" style="height: 400px;"></div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 学生成绩分布图表 -->
                    <div class="row mb-4">
                        <div class="col-md-6">
                            <div class="card">
                                <div class="card-header">
                                    <h6 class="card-title mb-0">成绩分布直方图</h6>
                                </div>
                                <div class="card-body">
                                    <div id="scoreHistogramChart" style="height: 300px;"></div>
                                </div>
                            </div>
                        </div>

                        <div class="col-md-6">
                            <div class="card">
                                <div class="card-header">
                                    <h6 class="card-title mb-0">成绩构成分析</h6>
                                </div>
                                <div class="card-body">
                                    <div id="scoreCompositionChart" style="height: 300px;"></div>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- 时间点选择器 -->
                    <div class="row mb-3">
                        <div class="col-12">
                            <div class="card">
                                <div class="card-header">
                                    <h6 class="card-title mb-0">
                                        <i class="fas fa-calendar-alt me-2"></i>
                                        时间段选择
                                    </h6>
                                </div>
                                <div class="card-body">
                                    <div class="row g-3">
                                        <div class="col-md-4">
                                            <label for="timeAcademicYear" class="form-label">学年</label>
                                            <select class="form-select" id="timeAcademicYear" name="time_academic_year">
                                                <option value="">请选择学年</option>
                                            </select>
                                        </div>
                                        <div class="col-md-4">
                                            <label for="timeSemester" class="form-label">学期</label>
                                            <select class="form-select" id="timeSemester" name="time_semester">
                                                <option value="">请选择学期</option>
                                                <option value="1">第一学期</option>
                                                <option value="2">第二学期</option>
                                            </select>
                                        </div>
                                        <div class="col-md-4">
                                            <label class="form-label">&nbsp;</label>
                                            <div class="d-grid">
                                                <button type="button" class="btn btn-outline-primary" id="applyTimeFilterBtn">
                                                    <i class="fas fa-filter me-1"></i>应用筛选
                                                </button>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 学生详细列表 -->
                    <div class="table-responsive">
                        <table class="table table-hover" id="classStudentsTable">
                            <thead>
                                <tr>
                                    <th>排名</th>
                                    <th>学号</th>
                                    <th>姓名</th>
                                    <th>总分</th>
                                    <th>学业成绩</th>
                                    <th>综合素质分</th>
                                    <th>奖学金等级</th>
                                </tr>
                            </thead>
                            <tbody id="classStudentsTableBody">
                                <!-- 学生数据将通过JavaScript动态加载 -->
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- AI分析结果 -->
<div id="aiAnalysisResult" class="ai-analysis" style="display: none;">
    <div class="card-header">
        <h5 class="card-title mb-0">
            <i class="fas fa-robot me-2"></i>
            AI智能分析报告
        </h5>
    </div>
    <div class="card-body">
        <div id="aiAnalysisContent" class="ai-content">
            <!-- AI分析内容将通过JavaScript动态加载 -->
        </div>
    </div>
</div>
{% endblock %}

{% block extra_scripts %}
<script>
$(document).ready(function() {
    let currentClassData = [];
    let selectedClass = null;
    let currentFilters = {};
    let availableSemesters = [];

    // 初始化页面
    initializePage();

    // 筛选表单提交
    $('#filterForm').on('submit', function(e) {
        e.preventDefault();
        loadClassData();
    });

    // 重置按钮
    $('#resetBtn').on('click', function() {
        console.log('重置筛选条件');
        $('#filterForm')[0].reset();
        $('#grade').val('');
        resetPageState();
        console.log('筛选条件已重置');
    });

    // 返回概览按钮
    $('#backToOverviewBtn').on('click', function() {
        // 重置选中的班级
        selectedClass = null;

        // 如果有班级数据，显示概览；否则提示重新查询
        if (currentClassData && currentClassData.length > 0) {
            showClassOverview();
        } else {
            hideAllSections();
            const noDataHtml = `
                <div class="row">
                    <div class="col-12">
                        <div class="alert alert-info">
                            <i class="fas fa-info-circle me-2"></i>
                            请先设置筛选条件并点击"查询"按钮获取班级数据。
                        </div>
                    </div>
                </div>
            `;
            $('#classOverview').html(noDataHtml).show();
        }
    });

    // AI分析按钮
    $('#aiAnalysisBtn').on('click', function() {
        generateClassAIAnalysis();
    });

    // 时间筛选按钮
    $('#applyTimeFilterBtn').on('click', function() {
        applyTimeFilter();
    });

    // 学年变化时更新时间选择器
    $('#academicYear').on('change', function() {
        updateGradeOptions();
        updateTimeOptions();
    });

    // 专业变化时更新年级选项
    $('#major').on('change', function() {
        updateGradeOptions();
    });
});

function initializePage() {
    // 加载可用的学期数据用于时间选择器
    $.get('/api/filters')
        .done(function(response) {
            if (response.success && response.data.semesters) {
                availableSemesters = response.data.semesters;
                updateTimeOptions();
            }
        })
        .fail(function() {
            console.warn('无法加载学期数据');
        });
}

function updateGradeOptions() {
    const academicYear = $('#academicYear').val();
    const major = $('#major').val();

    if (!academicYear) {
        $('#grade').html('<option value="">全部年级</option>');
        return;
    }

    // 根据学年计算可能的年级
    const currentYear = new Date().getFullYear();
    const startYear = parseInt(academicYear.split('-')[0]);
    const grades = [];

    // 通常大学有4个年级
    for (let i = 1; i <= 4; i++) {
        const gradeYear = startYear - i + 1;
        if (gradeYear > 0) {
            grades.push(gradeYear);
        }
    }

    let gradeOptions = '<option value="">全部年级</option>';
    grades.forEach(grade => {
        gradeOptions += `<option value="${grade}">${grade}级</option>`;
    });

    $('#grade').html(gradeOptions);
}

function updateTimeOptions() {
    let timeOptions = '<option value="">请选择学年</option>';
    availableSemesters.forEach(sem => {
        timeOptions += `<option value="${sem.academic_year}">${sem.academic_year}</option>`;
    });
    $('#timeAcademicYear').html(timeOptions);
}

function loadClassData() {
    const formData = new FormData($('#filterForm')[0]);
    const academicYear = formData.get('academic_year');
    const semester = formData.get('semester');
    const major = formData.get('major');
    const grade = formData.get('grade');

    if (!academicYear || !semester) {
        alert('请选择学年和学期');
        return;
    }

    // 清理筛选条件，确保空值不被传递
    currentFilters = {
        academic_year: academicYear,
        semester: semester,
        major: major || '',  // 确保空值转换为空字符串
        grade: grade || ''   // 确保空值转换为空字符串
    };

    // 记录筛选条件用于调试
    console.log('当前筛选条件:', currentFilters);

    // 重置选中的班级状态
    selectedClass = null;

    showLoading(true);
    hideAllSections();

    // 清除可能存在的错误状态
    $('#classOverview').empty();

    // 构建查询参数，确保空值不被传递
    const params = new URLSearchParams();
    if (academicYear) params.append('academic_year', academicYear);
    if (semester) params.append('semester', semester);
    if (major && major.trim()) params.append('major', major.trim());
    if (grade && grade.trim()) params.append('grade', grade.trim());

    console.log('发送请求参数:', params.toString());

    $.get('/analysis/api/class/data?' + params.toString())
        .done(function(response) {
            console.log('班级数据API响应:', response);
            if (response.success) {
                currentClassData = response.data;
                console.log('班级数据:', response.data);
                console.log('班级数据长度:', response.data ? response.data.length : 'undefined');

                // 确保数据存在且有效
                if (response.data && Array.isArray(response.data) && response.data.length > 0) {
                    console.log('开始显示班级概览');
                    displayClassOverview(response.data);
                } else {
                    console.warn('API返回空数据或无效数据');
                    showError('未找到符合条件的班级数据');
                }
            } else {
                console.error('API返回错误:', response.error);
                showError('加载数据失败: ' + response.error);
            }
        })
        .fail(function(xhr, status, error) {
            console.error('API请求失败:', status, error, xhr.responseText);
            showError('网络错误，请稍后重试');
        })
        .always(function() {
            showLoading(false);
        });
}

function displayClassOverview(data) {
    console.log('displayClassOverview被调用，数据:', data);

    if (!data || !Array.isArray(data) || data.length === 0) {
        console.warn('数据为空或无效');
        showError('未找到符合条件的班级数据');
        return;
    }

    console.log('开始显示统计卡片');
    // 显示统计卡片
    displayStatsCards(data);

    console.log('开始显示班级卡片');
    // 显示班级卡片
    displayClassCards(data);

    console.log('开始绘制图表');
    // 绘制图表
    drawClassComparisonChart(data);
    drawScoreDistributionChart(data);

    console.log('显示概览区域');
    // 显示概览区域
    showClassOverview();

    console.log('班级概览显示完成');
}

function displayStatsCards(data) {
    console.log('displayStatsCards被调用，数据:', data);

    try {
        const totalClasses = data.length;
        const totalStudents = data.reduce((sum, cls) => sum + (cls.student_count || 0), 0);
        const avgScore = data.reduce((sum, cls) => sum + (cls.avg_total_score || 0), 0) / data.length;
        const topClass = data.reduce((max, cls) => (cls.avg_total_score || 0) > (max.avg_total_score || 0) ? cls : max, data[0]);

        const statsHtml = `
            <div class="col-md-3">
                <div class="card stats-card">
                    <div class="card-body text-center">
                        <div class="stats-number">${totalClasses}</div>
                        <div class="stats-label">班级总数</div>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card stats-card">
                    <div class="card-body text-center">
                        <div class="stats-number">${totalStudents}</div>
                        <div class="stats-label">学生总数</div>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card stats-card">
                    <div class="card-body text-center">
                        <div class="stats-number">${avgScore.toFixed(2)}</div>
                        <div class="stats-label">平均总分</div>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card stats-card">
                    <div class="card-body text-center">
                        <div class="stats-number">${topClass.class_name || '未知'}</div>
                        <div class="stats-label">最优班级</div>
                    </div>
                </div>
            </div>
        `;

        console.log('统计卡片HTML:', statsHtml);
        $('#statsCards').html(statsHtml);
        console.log('统计卡片已更新');
    } catch (error) {
        console.error('显示统计卡片时出错:', error);
    }
}

function displayClassCards(data) {
    let cardsHtml = '';

    data.forEach((cls, index) => {
        const rankBadge = index < 3 ?
            `<span class="badge ${getRankBadgeClass(index + 1)}">#${index + 1}</span>` :
            `<span class="badge bg-light text-dark">#${index + 1}</span>`;

        cardsHtml += `
            <div class="col-md-4 mb-3">
                <div class="card class-card" data-class="${cls.class_name}">
                    <div class="card-body">
                        <div class="d-flex justify-content-between align-items-start mb-2">
                            <h6 class="card-title mb-0">${cls.class_name}</h6>
                            ${rankBadge}
                        </div>
                        <div class="row text-center">
                            <div class="col-4">
                                <small class="text-muted">学生数</small>
                                <div class="fw-bold">${cls.student_count}</div>
                            </div>
                            <div class="col-4">
                                <small class="text-muted">平均分</small>
                                <div class="fw-bold text-primary">${cls.avg_total_score.toFixed(2)}</div>
                            </div>
                            <div class="col-4">
                                <small class="text-muted">最高分</small>
                                <div class="fw-bold text-success">${cls.max_total_score.toFixed(2)}</div>
                            </div>
                        </div>
                        <div class="mt-2">
                            <small class="text-muted">分数范围: ${cls.min_total_score.toFixed(2)} - ${cls.max_total_score.toFixed(2)}</small>
                        </div>
                    </div>
                </div>
            </div>
        `;
    });

    $('#classCards').html(cardsHtml);

    // 添加点击事件
    $('.class-card').on('click', function() {
        const className = $(this).data('class');
        selectClass(className);
    });
}

function selectClass(className) {
    selectedClass = className;

    // 更新选中状态
    $('.class-card').removeClass('selected');
    $(`.class-card[data-class="${className}"]`).addClass('selected');

    // 加载班级详细数据
    loadClassDetailData(className);
}

function applyTimeFilter() {
    if (!selectedClass) {
        alert('请先选择一个班级');
        return;
    }

    const timeAcademicYear = $('#timeAcademicYear').val();
    const timeSemester = $('#timeSemester').val();

    if (!timeAcademicYear || !timeSemester) {
        alert('请选择学年和学期');
        return;
    }

    const timeFilters = {
        academic_year: timeAcademicYear,
        semester: timeSemester,
        major: currentFilters.major,
        grade: currentFilters.grade
    };

    showLoading(true);

    const params = new URLSearchParams(timeFilters);
    $.get('/ranking/api/data?' + params.toString())
        .done(function(response) {
            if (response.success) {
                const classStudents = response.data.filter(student =>
                    student.class_name === selectedClass
                );
                displayClassStudentsTable(classStudents);
            } else {
                showError('加载时间段数据失败: ' + response.error);
            }
        })
        .fail(function() {
            showError('网络错误，请稍后重试');
        })
        .always(function() {
            showLoading(false);
        });
}

function loadClassDetailData(className) {
    showLoading(true);

    // 构建查询参数，确保只传递有效值
    const params = new URLSearchParams();
    if (currentFilters.academic_year) params.append('academic_year', currentFilters.academic_year);
    if (currentFilters.semester) params.append('semester', currentFilters.semester);
    if (currentFilters.major && currentFilters.major.trim()) params.append('major', currentFilters.major.trim());
    if (currentFilters.grade && currentFilters.grade.trim()) params.append('grade', currentFilters.grade.trim());

    console.log('班级详情查询参数:', params.toString());

    // 获取排行榜数据来显示班级内学生详情
    $.get('/ranking/api/data?' + params.toString())
        .done(function(response) {
            console.log('班级详情API响应:', response);
            if (response.success) {
                // 筛选指定班级的学生
                const classStudents = response.data.filter(student =>
                    student.class_name === className
                );

                console.log(`班级 ${className} 学生数据:`, classStudents);

                if (classStudents.length === 0) {
                    // 不使用showError，而是显示班级详情页面但提示无数据
                    displayClassDetailWithNoData(className);
                } else {
                    displayClassDetail(classStudents, className);
                }

                // 加载班级历年趋势数据
                loadClassTrendData(className);
            } else {
                showError('加载班级详细数据失败: ' + response.error);
            }
        })
        .fail(function(xhr, status, error) {
            console.error('班级详情API请求失败:', status, error);
            showError('网络错误，请稍后重试');
        })
        .always(function() {
            showLoading(false);
        });
}

function loadClassTrendData(className) {
    // 获取该班级的历年数据
    const trendParams = new URLSearchParams({
        class_name: className,
        major: currentFilters.major || '',
        grade: currentFilters.grade || ''
    });

    $.get('/analysis/api/class/trend?' + trendParams.toString())
        .done(function(response) {
            if (response.success) {
                drawClassTrendChart(response.data);
            } else {
                console.warn('无法加载班级趋势数据:', response.error);
            }
        })
        .fail(function() {
            console.warn('加载班级趋势数据失败');
        });
}

function displayClassDetail(students, className) {
    $('#selectedClassName').text(className);

    // 显示班级统计信息
    displayClassDetailStats(students);

    // 显示学生列表
    displayClassStudentsTable(students);

    // 绘制班级详细图表
    drawScoreHistogramChart(students);
    drawScoreCompositionChart(students);

    // 显示班级详情区域
    showClassDetail();
}

function displayClassDetailWithNoData(className) {
    $('#selectedClassName').text(className);

    // 显示无数据的统计信息
    const noDataStatsHtml = `
        <div class="col-12">
            <div class="alert alert-warning">
                <i class="fas fa-info-circle me-2"></i>
                当前筛选条件下，班级 <strong>${className}</strong> 暂无学生数据。
                <br><small>请尝试调整筛选条件或检查数据是否已导入。</small>
            </div>
        </div>
    `;
    $('#classDetailStats').html(noDataStatsHtml);

    // 显示无数据的学生列表
    displayClassStudentsTable([]);

    // 清空图表区域
    $('#scoreHistogramChart').empty();
    $('#scoreCompositionChart').empty();

    // 显示班级详情区域
    showClassDetail();
}

function displayClassDetailStats(students) {
    if (students.length === 0) return;

    const totalStudents = students.length;
    const avgScore = students.reduce((sum, s) => sum + parseFloat(s.total_score || 0), 0) / totalStudents;
    const maxScore = Math.max(...students.map(s => parseFloat(s.total_score || 0)));
    const minScore = Math.min(...students.map(s => parseFloat(s.total_score || 0)));

    // 计算奖学金获得者数量
    const awardWinners = students.filter(s => s.award_level && s.award_level !== '无').length;

    const statsHtml = `
        <div class="col-md-3">
            <div class="card stats-card">
                <div class="card-body text-center">
                    <div class="stats-number">${totalStudents}</div>
                    <div class="stats-label">班级人数</div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card stats-card">
                <div class="card-body text-center">
                    <div class="stats-number">${avgScore.toFixed(2)}</div>
                    <div class="stats-label">平均总分</div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card stats-card">
                <div class="card-body text-center">
                    <div class="stats-number">${maxScore.toFixed(2)}</div>
                    <div class="stats-label">最高分</div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card stats-card">
                <div class="card-body text-center">
                    <div class="stats-number">${awardWinners}</div>
                    <div class="stats-label">获奖人数</div>
                </div>
            </div>
        </div>
    `;

    $('#classDetailStats').html(statsHtml);
}

function displayClassStudentsTable(students) {
    const tbody = $('#classStudentsTableBody');
    tbody.empty();

    if (students.length === 0) {
        tbody.append(`
            <tr>
                <td colspan="7" class="text-center text-muted">
                    <i class="fas fa-inbox fa-2x mb-2"></i><br>
                    暂无学生数据
                </td>
            </tr>
        `);
        return;
    }

    // 按总分排序
    students.sort((a, b) => parseFloat(b.total_score || 0) - parseFloat(a.total_score || 0));

    students.forEach((student, index) => {
        const row = `
            <tr>
                <td>
                    <span class="badge ${getRankBadgeClass(index + 1)}">${index + 1}</span>
                </td>
                <td>${student.student_id}</td>
                <td>${student.name}</td>
                <td>
                    <span class="badge bg-primary">${parseFloat(student.total_score || 0).toFixed(2)}</span>
                </td>
                <td>${parseFloat(student.academic_score || 0).toFixed(2)}</td>
                <td>${parseFloat(student.comprehensive_score || 0).toFixed(2)}</td>
                <td>
                    ${student.award_level && student.award_level !== '无' ?
                        `<span class="badge ${getAwardBadgeClass(student.award_level)}">${student.award_level}</span>` :
                        '<span class="text-muted">无</span>'
                    }
                </td>
            </tr>
        `;
        tbody.append(row);
    });
}

function generateClassAIAnalysis() {
    if (!selectedClass) {
        alert('请先选择一个班级');
        return;
    }

    const button = $('#aiAnalysisBtn');
    const originalText = button.html();

    button.prop('disabled', true).html('<i class="fas fa-spinner fa-spin me-1"></i>分析中...');

    const requestData = {
        academic_year: currentFilters.academic_year,
        semester: currentFilters.semester,
        major: currentFilters.major,
        class_name: selectedClass
    };

    $.ajax({
        url: '/analysis/api/class/ai_analysis',
        method: 'POST',
        contentType: 'application/json',
        data: JSON.stringify(requestData),
        timeout: 300000 // 5分钟超时
    })
    .done(function(response) {
        if (response.success) {
            displayAIAnalysisResult(response.data.analysis);
        } else {
            alert('AI分析失败: ' + response.error);
        }
    })
    .fail(function(xhr) {
        if (xhr.status === 0) {
            alert('请求超时，请稍后重试');
        } else {
            alert('AI分析失败，请稍后重试');
        }
    })
    .always(function() {
        button.prop('disabled', false).html(originalText);
    });
}

function displayAIAnalysisResult(analysis) {
    try {
        // 尝试使用marked.js渲染markdown
        if (typeof marked !== 'undefined') {
            const htmlContent = marked.parse(analysis);
            $('#aiAnalysisContent').html(htmlContent);

            // 应用代码高亮
            if (typeof hljs !== 'undefined') {
                hljs.highlightAll();
            }
        } else {
            // 备用方案：显示原始内容
            $('#aiAnalysisContent').html('<pre>' + analysis + '</pre>');
        }
    } catch (error) {
        console.error('Markdown渲染失败:', error);
        $('#aiAnalysisContent').html('<pre>' + analysis + '</pre>');
    }

    $('#aiAnalysisResult').show();

    // 滚动到AI分析结果
    $('html, body').animate({
        scrollTop: $('#aiAnalysisResult').offset().top - 100
    }, 500);
}

// 图表绘制函数
function drawClassComparisonChart(data) {
    console.log('绘制班级对比图表，数据:', data);

    const chartElement = document.getElementById('classComparisonChart');
    if (!chartElement) {
        console.error('找不到图表容器: classComparisonChart');
        return;
    }

    // 确保ECharts已加载
    if (typeof echarts === 'undefined') {
        console.error('ECharts未加载');
        return;
    }

    // 销毁已存在的图表实例
    echarts.dispose(chartElement);
    const chart = echarts.init(chartElement);

    const option = {
        tooltip: {
            trigger: 'axis',
            axisPointer: {
                type: 'shadow'
            }
        },
        legend: {
            data: ['平均总分', '平均学业成绩', '平均综合素质分']
        },
        xAxis: {
            type: 'category',
            data: data.map(cls => cls.class_name),
            axisLabel: {
                rotate: 45
            }
        },
        yAxis: {
            type: 'value',
            name: '分数'
        },
        series: [
            {
                name: '平均总分',
                type: 'bar',
                data: data.map(cls => parseFloat(cls.avg_total_score || 0).toFixed(2)),
                itemStyle: { color: '#4facfe' }
            },
            {
                name: '平均学业成绩',
                type: 'bar',
                data: data.map(cls => parseFloat(cls.avg_academic_score || 0).toFixed(2)),
                itemStyle: { color: '#28a745' }
            },
            {
                name: '平均综合素质分',
                type: 'bar',
                data: data.map(cls => parseFloat(cls.avg_comprehensive_score || 0).toFixed(2)),
                itemStyle: { color: '#ffc107' }
            }
        ]
    };

    chart.setOption(option);

    // 窗口大小改变时重新调整图表
    window.addEventListener('resize', function() {
        chart.resize();
    });
}

function drawClassTrendChart(data) {
    console.log('绘制班级趋势图表，数据:', data);

    const chartElement = document.getElementById('classTrendChart');
    if (!chartElement) {
        console.error('找不到图表容器: classTrendChart');
        return;
    }

    if (typeof echarts === 'undefined') {
        console.error('ECharts未加载');
        return;
    }

    echarts.dispose(chartElement);
    const chart = echarts.init(chartElement);

    // 按学期排序数据
    data.sort((a, b) => {
        if (a.academic_year !== b.academic_year) {
            return a.academic_year.localeCompare(b.academic_year);
        }
        return a.semester - b.semester;
    });

    const xAxisData = data.map(item => `${item.academic_year}-${item.semester}`);

    const option = {
        tooltip: {
            trigger: 'axis',
            axisPointer: {
                type: 'cross'
            }
        },
        legend: {
            data: ['平均总分', '平均学业成绩', '平均综合素质分']
        },
        xAxis: {
            type: 'category',
            data: xAxisData,
            axisLabel: {
                rotate: 45
            }
        },
        yAxis: {
            type: 'value',
            name: '分数'
        },
        series: [
            {
                name: '平均总分',
                type: 'line',
                data: data.map(item => parseFloat(item.avg_total_score || 0).toFixed(2)),
                itemStyle: { color: '#4facfe' },
                lineStyle: { color: '#4facfe' },
                smooth: true
            },
            {
                name: '平均学业成绩',
                type: 'line',
                data: data.map(item => parseFloat(item.avg_academic_score || 0).toFixed(2)),
                itemStyle: { color: '#28a745' },
                lineStyle: { color: '#28a745' },
                smooth: true
            },
            {
                name: '平均综合素质分',
                type: 'line',
                data: data.map(item => parseFloat(item.avg_comprehensive_score || 0).toFixed(2)),
                itemStyle: { color: '#ffc107' },
                lineStyle: { color: '#ffc107' },
                smooth: true
            }
        ]
    };

    chart.setOption(option);

    window.addEventListener('resize', function() {
        chart.resize();
    });
}

function drawScoreDistributionChart(data) {
    console.log('绘制成绩分布图表，数据:', data);

    const chartElement = document.getElementById('scoreDistributionChart');
    if (!chartElement) {
        console.error('找不到图表容器: scoreDistributionChart');
        return;
    }

    if (typeof echarts === 'undefined') {
        console.error('ECharts未加载');
        return;
    }

    echarts.dispose(chartElement);
    const chart = echarts.init(chartElement);

    // 计算分数段分布
    const scoreRanges = ['90-100', '80-89', '70-79', '60-69', '60以下'];
    const distribution = scoreRanges.map(range => {
        let count = 0;
        data.forEach(cls => {
            const avgScore = parseFloat(cls.avg_total_score || 0);
            if (range === '90-100' && avgScore >= 90) count++;
            else if (range === '80-89' && avgScore >= 80 && avgScore < 90) count++;
            else if (range === '70-79' && avgScore >= 70 && avgScore < 80) count++;
            else if (range === '60-69' && avgScore >= 60 && avgScore < 70) count++;
            else if (range === '60以下' && avgScore < 60) count++;
        });
        return { name: range, value: count };
    });

    const option = {
        tooltip: {
            trigger: 'item',
            formatter: '{a} <br/>{b}: {c} ({d}%)'
        },
        legend: {
            orient: 'vertical',
            left: 'left',
            data: scoreRanges
        },
        series: [
            {
                name: '班级分布',
                type: 'pie',
                radius: '50%',
                data: distribution,
                emphasis: {
                    itemStyle: {
                        shadowBlur: 10,
                        shadowOffsetX: 0,
                        shadowColor: 'rgba(0, 0, 0, 0.5)'
                    }
                }
            }
        ]
    };

    chart.setOption(option);

    window.addEventListener('resize', function() {
        chart.resize();
    });
}

function drawScoreHistogramChart(students) {
    const chartElement = document.getElementById('scoreHistogramChart');
    if (!chartElement) {
        console.error('找不到图表容器: scoreHistogramChart');
        return;
    }

    if (typeof echarts === 'undefined') {
        console.error('ECharts未加载');
        return;
    }

    echarts.dispose(chartElement);
    const chart = echarts.init(chartElement);

    // 计算分数分布
    const scores = students.map(s => parseFloat(s.total_score || 0));
    const bins = [0, 60, 70, 80, 90, 100];
    const binLabels = ['0-60', '60-70', '70-80', '80-90', '90-100'];
    const distribution = binLabels.map((label, index) => {
        const min = bins[index];
        const max = bins[index + 1];
        const count = scores.filter(score => score >= min && (index === binLabels.length - 1 ? score <= max : score < max)).length;
        return count;
    });

    const option = {
        tooltip: {
            trigger: 'axis',
            axisPointer: {
                type: 'shadow'
            }
        },
        xAxis: {
            type: 'category',
            data: binLabels,
            name: '分数段'
        },
        yAxis: {
            type: 'value',
            name: '人数'
        },
        series: [
            {
                name: '学生人数',
                type: 'bar',
                data: distribution,
                itemStyle: { color: '#4facfe' }
            }
        ]
    };

    chart.setOption(option);

    window.addEventListener('resize', function() {
        chart.resize();
    });
}

function drawScoreCompositionChart(students) {
    const chartElement = document.getElementById('scoreCompositionChart');
    if (!chartElement) {
        console.error('找不到图表容器: scoreCompositionChart');
        return;
    }

    if (typeof echarts === 'undefined') {
        console.error('ECharts未加载');
        return;
    }

    echarts.dispose(chartElement);
    const chart = echarts.init(chartElement);

    // 计算平均分构成
    const avgAcademic = students.reduce((sum, s) => sum + parseFloat(s.academic_score || 0), 0) / students.length;
    const avgComprehensive = students.reduce((sum, s) => sum + parseFloat(s.comprehensive_score || 0), 0) / students.length;

    const option = {
        tooltip: {
            trigger: 'item',
            formatter: '{a} <br/>{b}: {c} ({d}%)'
        },
        legend: {
            orient: 'vertical',
            left: 'left'
        },
        series: [
            {
                name: '成绩构成',
                type: 'pie',
                radius: '50%',
                data: [
                    { value: parseFloat(avgAcademic.toFixed(2)), name: '学业成绩' },
                    { value: parseFloat(avgComprehensive.toFixed(2)), name: '综合素质分' }
                ],
                emphasis: {
                    itemStyle: {
                        shadowBlur: 10,
                        shadowOffsetX: 0,
                        shadowColor: 'rgba(0, 0, 0, 0.5)'
                    }
                }
            }
        ]
    };

    chart.setOption(option);

    window.addEventListener('resize', function() {
        chart.resize();
    });
}

// 工具函数
function getRankBadgeClass(rank) {
    if (rank === 1) return 'bg-warning';
    if (rank === 2) return 'bg-secondary';
    if (rank === 3) return 'bg-info';
    if (rank <= 10) return 'bg-success';
    return 'bg-light text-dark';
}

function getAwardBadgeClass(award) {
    if (award.includes('一等')) return 'bg-warning';
    if (award.includes('二等')) return 'bg-info';
    if (award.includes('三等')) return 'bg-success';
    return 'bg-secondary';
}

// 界面控制函数
function showLoading(show) {
    if (show) {
        $('#loadingSpinner').show();
    } else {
        $('#loadingSpinner').hide();
    }
}

function hideAllSections() {
    $('#classOverview').hide();
    $('#classDetailAnalysis').hide();
    $('#aiAnalysisResult').hide();
}

function showClassOverview() {
    hideAllSections();
    $('#classOverview').show();
    selectedClass = null;
    $('.class-card').removeClass('selected');
}

function showClassDetail() {
    hideAllSections();
    $('#classDetailAnalysis').show();
    $('#aiAnalysisResult').hide();
}

function showError(message) {
    hideAllSections();

    const errorHtml = `
        <div class="row">
            <div class="col-12">
                <div class="alert alert-danger">
                    <i class="fas fa-exclamation-triangle me-2"></i>
                    ${message}
                </div>
            </div>
        </div>
    `;

    $('#classOverview').html(errorHtml).show();
}

function resetPageState() {
    // 重置所有状态变量
    currentClassData = [];
    selectedClass = null;
    currentFilters = {};

    // 隐藏所有区域
    hideAllSections();

    // 清空所有显示区域的内容
    $('#statsCards').empty();
    $('#classCards').empty();
    $('#classDetailStats').empty();
    $('#classStudentsTableBody').empty();
    $('#aiAnalysisResult').hide();
    $('#aiAnalysisContent').empty();

    // 清空图表区域
    $('#classComparisonChart').empty();
    $('#scoreDistributionChart').empty();
    $('#scoreHistogramChart').empty();
    $('#scoreCompositionChart').empty();
    $('#classTrendChart').empty();

    // 重置classOverview区域，清除可能的错误信息
    $('#classOverview').empty().hide();

    console.log('页面状态已完全重置');
}
</script>
{% endblock %}
