#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
调试年级筛选问题
"""

import sqlite3
from models.scholarship import ScholarshipData

def check_grade_data():
    """检查数据库中的年级数据"""
    print("=" * 50)
    print("年级数据检查")
    print("=" * 50)
    
    try:
        conn = sqlite3.connect('scholarship_data.db')
        conn.row_factory = sqlite3.Row
        cursor = conn.cursor()
        
        # 检查所有年级数据
        print("\n📊 所有年级数据:")
        cursor.execute("""
            SELECT DISTINCT grade, COUNT(*) as count
            FROM scores 
            WHERE grade IS NOT NULL
            GROUP BY grade 
            ORDER BY grade DESC
        """)
        grades = cursor.fetchall()
        
        for grade in grades:
            print(f"  年级 {grade['grade']}: {grade['count']} 条记录")
        
        # 检查物联网专业的年级数据
        print("\n🔍 物联网专业年级数据:")
        cursor.execute("""
            SELECT DISTINCT sc.grade, COUNT(*) as count, sc.class_name
            FROM scores sc
            JOIN majors m ON sc.major_id = m.id
            JOIN semesters sem ON sc.semester_id = sem.id
            WHERE m.major_name LIKE '%物联网%' 
            AND sem.academic_year = '2024-2025' 
            AND sem.semester = 1
            GROUP BY sc.grade, sc.class_name
            ORDER BY sc.grade DESC, sc.class_name
        """)
        iot_grades = cursor.fetchall()
        
        for grade in iot_grades:
            print(f"  年级 {grade['grade']}, 班级 {grade['class_name']}: {grade['count']} 人")
        
        # 检查2024年级的数据
        print("\n🎯 2024年级数据详情:")
        cursor.execute("""
            SELECT sc.class_name, m.major_name, COUNT(*) as count
            FROM scores sc
            JOIN majors m ON sc.major_id = m.id
            JOIN semesters sem ON sc.semester_id = sem.id
            WHERE sc.grade = 2024
            AND sem.academic_year = '2024-2025' 
            AND sem.semester = 1
            GROUP BY sc.class_name, m.major_name
            ORDER BY m.major_name, sc.class_name
        """)
        grade_2024 = cursor.fetchall()
        
        if grade_2024:
            for record in grade_2024:
                print(f"  {record['major_name']} - {record['class_name']}: {record['count']} 人")
        else:
            print("  ❌ 没有找到2024年级的数据")
        
        conn.close()
        return True
        
    except Exception as e:
        print(f"❌ 检查年级数据失败: {e}")
        return False

def test_scholarship_data_with_grade():
    """测试ScholarshipData的年级筛选功能"""
    print("\n" + "=" * 50)
    print("ScholarshipData年级筛选测试")
    print("=" * 50)
    
    try:
        scholarship_data = ScholarshipData()
        
        # 测试不带年级筛选的查询
        print("\n🔍 测试不带年级筛选:")
        class_data_no_grade = scholarship_data.get_class_analysis_data(
            academic_year='2024-2025',
            semester=1,
            major='物联网'
        )
        print(f"  返回班级数: {len(class_data_no_grade)}")
        
        if class_data_no_grade:
            print("  班级列表:")
            for cls in class_data_no_grade:
                print(f"    - {cls['class_name']} (年级: {cls['grade']}): {cls['student_count']}人")
        
        # 测试带年级筛选的查询 (grade=2024)
        print("\n🎯 测试年级筛选 (grade=2024):")
        class_data_with_grade = scholarship_data.get_class_analysis_data(
            academic_year='2024-2025',
            semester=1,
            major='物联网',
            grade=2024
        )
        print(f"  返回班级数: {len(class_data_with_grade)}")
        
        if class_data_with_grade:
            print("  班级列表:")
            for cls in class_data_with_grade:
                print(f"    - {cls['class_name']} (年级: {cls['grade']}): {cls['student_count']}人")
        else:
            print("  ❌ 没有找到符合条件的班级")
        
        # 测试其他年级
        print("\n🔍 测试其他年级:")
        for test_grade in [2023, 2022, 2021]:
            class_data_test = scholarship_data.get_class_analysis_data(
                academic_year='2024-2025',
                semester=1,
                major='物联网',
                grade=test_grade
            )
            print(f"  年级 {test_grade}: {len(class_data_test)} 个班级")
        
        return True
        
    except Exception as e:
        print(f"❌ ScholarshipData年级筛选测试失败: {e}")
        return False

def main():
    """主函数"""
    print("🔧 年级筛选问题调试")
    
    tests = [
        ("年级数据检查", check_grade_data),
        ("ScholarshipData年级筛选测试", test_scholarship_data_with_grade)
    ]
    
    results = []
    for test_name, test_func in tests:
        print(f"\n🧪 {test_name}")
        result = test_func()
        results.append((test_name, result))
    
    print("\n" + "=" * 50)
    print("调试结果汇总")
    print("=" * 50)
    
    for test_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{test_name}: {status}")

if __name__ == '__main__':
    main()
