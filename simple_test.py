#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简化的API测试脚本
"""

from models.scholarship import ScholarshipData

def test_scholarship_data():
    """直接测试ScholarshipData类"""
    print("=" * 50)
    print("直接测试ScholarshipData类")
    print("=" * 50)
    
    try:
        scholarship_data = ScholarshipData()
        
        # 测试1: 不带年级筛选
        print("\n测试1: 不带年级筛选")
        print("-" * 30)
        result1 = scholarship_data.get_class_analysis_data(
            academic_year='2024-2025',
            semester=1,
            major='物联网'
        )
        print(f"返回班级数: {len(result1)}")
        if result1:
            for cls in result1[:3]:
                print(f"  - {cls['class_name']} (年级: {cls['grade']}): {cls['student_count']}人")
        
        # 测试2: 带年级筛选 (2024)
        print("\n测试2: 带年级筛选 (2024)")
        print("-" * 30)
        result2 = scholarship_data.get_class_analysis_data(
            academic_year='2024-2025',
            semester=1,
            major='物联网',
            grade=2024
        )
        print(f"返回班级数: {len(result2)}")
        if result2:
            for cls in result2[:3]:
                print(f"  - {cls['class_name']} (年级: {cls['grade']}): {cls['student_count']}人")
        
        # 测试3: 再次不带年级筛选
        print("\n测试3: 再次不带年级筛选")
        print("-" * 30)
        result3 = scholarship_data.get_class_analysis_data(
            academic_year='2024-2025',
            semester=1,
            major='物联网'
        )
        print(f"返回班级数: {len(result3)}")
        if result3:
            for cls in result3[:3]:
                print(f"  - {cls['class_name']} (年级: {cls['grade']}): {cls['student_count']}人")
        
        # 检查是否存在状态污染
        if len(result1) == len(result3):
            print("\n✅ 没有状态污染问题")
        else:
            print(f"\n❌ 存在状态污染: 第一次{len(result1)}个班级，第三次{len(result3)}个班级")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == '__main__':
    test_scholarship_data()
