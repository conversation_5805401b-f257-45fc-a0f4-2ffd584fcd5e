#!/usr/bin/env python3
"""
测试前端修复效果的脚本
"""

import requests
import json
import time

BASE_URL = 'http://localhost:5000'

def test_class_analysis_display():
    """测试班级分析页面显示问题修复"""
    print("=" * 60)
    print("测试班级分析页面显示问题修复")
    print("=" * 60)
    
    session = requests.Session()
    
    try:
        # 1. 测试班级数据API
        print("\n步骤1: 测试班级数据API")
        class_params = {
            'academic_year': '2024-2025',
            'semester': 1,
            'major': '电子信息工程',
            'grade': 2024
        }
        
        class_response = session.get(f'{BASE_URL}/analysis/api/class/data', params=class_params)
        print(f"请求URL: {class_response.url}")
        print(f"响应状态: {class_response.status_code}")
        
        if class_response.status_code != 200:
            print(f"❌ 班级数据API请求失败: {class_response.status_code}")
            return False
        
        class_data = class_response.json()
        if not class_data.get('success'):
            print(f"❌ 班级数据API错误: {class_data.get('error')}")
            return False
        
        classes = class_data['data']
        print(f"✅ 获取到 {len(classes)} 个班级")
        
        if classes:
            print("前3个班级信息:")
            for i, cls in enumerate(classes[:3]):
                print(f"  班级 {i+1}: {cls['class_name']} - 学生数: {cls['student_count']}, 平均分: {cls['avg_total_score']:.2f}")
        
        # 2. 测试前端页面访问
        print("\n步骤2: 测试前端页面访问")
        page_response = session.get(f'{BASE_URL}/analysis/class')
        if page_response.status_code == 200:
            print("✅ 班级分析页面访问正常")
        else:
            print(f"❌ 班级分析页面访问失败: {page_response.status_code}")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ 测试过程中出现异常: {e}")
        return False

def test_ranking_major_parameter():
    """测试学生排行榜专业参数修复"""
    print("\n" + "=" * 60)
    print("测试学生排行榜专业参数修复")
    print("=" * 60)
    
    session = requests.Session()
    
    try:
        # 1. 测试正常的专业参数
        print("\n步骤1: 测试正常的专业参数")
        normal_params = {
            'academic_year': '2024-2025',
            'semester': 1,
            'major': '计算机科学与技术',
            'grade': '24',
            'page': 1,
            'per_page': 20
        }
        
        normal_response = session.get(f'{BASE_URL}/ranking/api/data', params=normal_params)
        print(f"请求URL: {normal_response.url}")
        print(f"响应状态: {normal_response.status_code}")
        
        if normal_response.status_code == 200:
            normal_data = normal_response.json()
            if normal_data.get('success'):
                print(f"✅ 正常参数请求成功，返回 {len(normal_data['data'])} 条记录")
            else:
                print(f"❌ 正常参数请求失败: {normal_data.get('error')}")
        else:
            print(f"❌ 正常参数请求失败: {normal_response.status_code}")
        
        # 2. 测试错误格式的专业参数（模拟之前的错误）
        print("\n步骤2: 测试错误格式的专业参数修复")
        error_params = {
            'academic_year': '2024-2025',
            'semester': 1,
            'major': "{'major_name': '计算机科学与技术'}",  # 模拟错误格式
            'grade': '24',
            'page': 1,
            'per_page': 20
        }
        
        error_response = session.get(f'{BASE_URL}/ranking/api/data', params=error_params)
        print(f"请求URL: {error_response.url}")
        print(f"响应状态: {error_response.status_code}")
        
        if error_response.status_code == 200:
            error_data = error_response.json()
            if error_data.get('success'):
                print(f"✅ 错误格式参数已被修复，返回 {len(error_data['data'])} 条记录")
            else:
                print(f"⚠️ 错误格式参数处理结果: {error_data.get('error')}")
        else:
            print(f"❌ 错误格式参数请求失败: {error_response.status_code}")
        
        # 3. 测试前端页面访问
        print("\n步骤3: 测试前端页面访问")
        page_response = session.get(f'{BASE_URL}/ranking')
        if page_response.status_code == 200:
            print("✅ 学生排行榜页面访问正常")
        else:
            print(f"❌ 学生排行榜页面访问失败: {page_response.status_code}")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ 测试过程中出现异常: {e}")
        return False

def test_frontend_javascript_errors():
    """测试前端JavaScript错误修复"""
    print("\n" + "=" * 60)
    print("测试前端JavaScript错误修复")
    print("=" * 60)
    
    session = requests.Session()
    
    try:
        # 测试筛选条件API
        print("\n步骤1: 测试筛选条件API")
        filters_response = session.get(f'{BASE_URL}/api/filters')
        print(f"请求URL: {filters_response.url}")
        print(f"响应状态: {filters_response.status_code}")
        
        if filters_response.status_code == 200:
            filters_data = filters_response.json()
            if filters_data.get('success'):
                print("✅ 筛选条件API正常")
                print(f"  学期数量: {len(filters_data['data']['semesters'])}")
                print(f"  专业数量: {len(filters_data['data']['majors'])}")
                print(f"  年级数量: {len(filters_data['data']['grades'])}")
            else:
                print(f"❌ 筛选条件API错误: {filters_data.get('error')}")
        else:
            print(f"❌ 筛选条件API请求失败: {filters_response.status_code}")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试过程中出现异常: {e}")
        return False

def main():
    """主测试函数"""
    print("开始测试前端修复效果...")
    print(f"测试目标: {BASE_URL}")
    
    # 等待服务器启动
    print("\n检查服务器状态...")
    try:
        response = requests.get(BASE_URL, timeout=5)
        if response.status_code == 200:
            print("✅ 服务器运行正常")
        else:
            print(f"⚠️ 服务器响应异常: {response.status_code}")
    except Exception as e:
        print(f"❌ 无法连接到服务器: {e}")
        print("请确保服务器已启动并运行在 http://localhost:5000")
        return
    
    # 执行测试
    results = []
    
    # 测试1: 班级分析页面显示问题
    results.append(("班级分析页面显示", test_class_analysis_display()))
    
    # 测试2: 学生排行榜专业参数
    results.append(("学生排行榜专业参数", test_ranking_major_parameter()))
    
    # 测试3: 前端JavaScript错误
    results.append(("前端JavaScript错误", test_frontend_javascript_errors()))
    
    # 输出测试结果
    print("\n" + "=" * 60)
    print("测试结果汇总")
    print("=" * 60)
    
    all_passed = True
    for test_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{test_name}: {status}")
        if not result:
            all_passed = False
    
    print("\n" + "=" * 60)
    if all_passed:
        print("🎉 所有测试通过！前端修复效果良好。")
    else:
        print("⚠️ 部分测试失败，需要进一步检查和修复。")
    print("=" * 60)

if __name__ == '__main__':
    main()
