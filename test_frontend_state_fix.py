#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试前端状态管理bug修复效果
"""

import requests
import json
import time

BASE_URL = 'http://127.0.0.1:5000'

def login_and_get_session():
    """登录并获取会话"""
    session = requests.Session()
    
    try:
        login_data = {'username': 'admin', 'password': 'admin123'}
        response = session.post(f'{BASE_URL}/auth/login', data=login_data)
        
        if response.status_code == 302:
            print("✅ 登录成功")
            return session
        else:
            print(f"❌ 登录失败: {response.status_code}")
            return None
            
    except Exception as e:
        print(f"❌ 登录异常: {e}")
        return None

def test_class_detail_data_availability():
    """测试班级详情数据的可用性"""
    print("=" * 60)
    print("测试班级详情数据可用性")
    print("=" * 60)
    
    session = login_and_get_session()
    if not session:
        return False
    
    try:
        # 1. 获取班级列表
        print("\n步骤1: 获取班级列表")
        class_params = {
            'academic_year': '2024-2025',
            'semester': 1,
            'major': '电子信息工程',
            'grade': 2024
        }
        
        class_response = session.get(f'{BASE_URL}/analysis/api/class/data', params=class_params)
        if class_response.status_code != 200:
            print(f"❌ 班级数据API请求失败: {class_response.status_code}")
            return False
        
        class_data = class_response.json()
        if not class_data.get('success'):
            print(f"❌ 班级数据API错误: {class_data.get('error')}")
            return False
        
        classes = class_data['data']
        print(f"✅ 获取到 {len(classes)} 个班级")
        
        if not classes:
            print("⚠️  没有班级数据，无法测试班级详情")
            return True
        
        # 找到电信2405班级（如果存在）
        target_class = None
        for cls in classes:
            if '电信2405' in cls['class_name']:
                target_class = cls['class_name']
                break
        
        if not target_class:
            target_class = classes[0]['class_name']
        
        print(f"  测试班级: {target_class}")
        
        # 2. 测试班级详情数据
        print(f"\n步骤2: 测试班级 {target_class} 的详情数据")
        ranking_params = {
            'academic_year': '2024-2025',
            'semester': 1,
            'major': '电子信息工程',
            'grade': 2024
        }
        
        ranking_response = session.get(f'{BASE_URL}/ranking/api/data', params=ranking_params)
        if ranking_response.status_code != 200:
            print(f"❌ 排行榜API请求失败: {ranking_response.status_code}")
            return False
        
        ranking_data = ranking_response.json()
        if not ranking_data.get('success'):
            print(f"❌ 排行榜API错误: {ranking_data.get('error')}")
            return False
        
        # 筛选指定班级的学生
        all_students = ranking_data['data']
        class_students = [s for s in all_students if s['class_name'] == target_class]
        
        print(f"  全部学生数: {len(all_students)}")
        print(f"  班级 {target_class} 学生数: {len(class_students)}")
        
        if len(class_students) == 0:
            print(f"⚠️  班级 {target_class} 在当前筛选条件下没有学生数据")
            print("  这可能是正常的，取决于数据和筛选条件")
        else:
            print(f"✅ 班级 {target_class} 有 {len(class_students)} 个学生")
            # 显示前3个学生信息
            for i, student in enumerate(class_students[:3]):
                print(f"    学生 {i+1}: {student['name']} ({student['student_id']}) - 总分: {student['total_score']}")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试异常: {e}")
        return False

def test_different_filter_combinations():
    """测试不同筛选条件组合"""
    print("\n" + "=" * 60)
    print("测试不同筛选条件组合")
    print("=" * 60)
    
    session = login_and_get_session()
    if not session:
        return False
    
    # 测试多种筛选条件组合
    test_cases = [
        {
            'name': '电子信息工程专业（无年级筛选）',
            'params': {
                'academic_year': '2024-2025',
                'semester': 1,
                'major': '电子信息工程'
            }
        },
        {
            'name': '电子信息工程专业 + 2024年级',
            'params': {
                'academic_year': '2024-2025',
                'semester': 1,
                'major': '电子信息工程',
                'grade': 2024
            }
        },
        {
            'name': '物联网专业（无年级筛选）',
            'params': {
                'academic_year': '2024-2025',
                'semester': 1,
                'major': '物联网'
            }
        },
        {
            'name': '物联网专业 + 2024年级',
            'params': {
                'academic_year': '2024-2025',
                'semester': 1,
                'major': '物联网',
                'grade': 2024
            }
        }
    ]
    
    results = []
    
    for i, test_case in enumerate(test_cases):
        print(f"\n测试 {i+1}: {test_case['name']}")
        print("-" * 40)
        
        try:
            # 测试班级数据API
            class_response = session.get(f'{BASE_URL}/analysis/api/class/data', params=test_case['params'])
            
            if class_response.status_code == 200:
                class_data = class_response.json()
                if class_data.get('success'):
                    class_count = len(class_data['data'])
                    print(f"  ✅ 班级数据: {class_count} 个班级")
                    
                    # 测试排行榜数据API
                    ranking_response = session.get(f'{BASE_URL}/ranking/api/data', params=test_case['params'])
                    
                    if ranking_response.status_code == 200:
                        ranking_data = ranking_response.json()
                        if ranking_data.get('success'):
                            student_count = len(ranking_data['data'])
                            print(f"  ✅ 学生数据: {student_count} 个学生")
                            results.append(True)
                        else:
                            print(f"  ❌ 学生数据API错误: {ranking_data.get('error')}")
                            results.append(False)
                    else:
                        print(f"  ❌ 学生数据API请求失败: {ranking_response.status_code}")
                        results.append(False)
                else:
                    print(f"  ❌ 班级数据API错误: {class_data.get('error')}")
                    results.append(False)
            else:
                print(f"  ❌ 班级数据API请求失败: {class_response.status_code}")
                results.append(False)
                
        except Exception as e:
            print(f"  ❌ 测试异常: {e}")
            results.append(False)
    
    success_count = sum(results)
    total_count = len(results)
    
    print(f"\n筛选条件测试结果: {success_count}/{total_count} 通过")
    return success_count == total_count

def main():
    """主函数"""
    print("🔧 前端状态管理bug修复测试")
    print("=" * 60)
    print("测试目标:")
    print("1. 验证班级详情数据的正确获取")
    print("2. 验证不同筛选条件下的数据可用性")
    print("3. 确认后端API正常返回数据")
    print("=" * 60)
    
    tests = [
        ("班级详情数据可用性", test_class_detail_data_availability),
        ("不同筛选条件组合", test_different_filter_combinations)
    ]
    
    results = []
    for test_name, test_func in tests:
        print(f"\n🧪 {test_name}")
        result = test_func()
        results.append((test_name, result))
    
    print("\n" + "=" * 60)
    print("测试结果汇总")
    print("=" * 60)
    
    passed = 0
    for test_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\n总计: {passed}/{len(results)} 个测试通过")
    
    if passed == len(results):
        print("\n🎉 后端API测试全部通过！")
        print("\n📝 修复说明:")
        print("1. ✅ 改进了班级详情错误处理逻辑")
        print("2. ✅ 添加了专门的无数据显示函数")
        print("3. ✅ 改进了页面状态重置机制")
        print("4. ✅ 修复了前端状态污染问题")
        print("5. ✅ 增强了错误状态的清除能力")
        print("\n🌐 请在浏览器中测试前端交互效果")
    else:
        print("\n⚠️  部分测试失败，需要进一步调试")

if __name__ == '__main__':
    main()
