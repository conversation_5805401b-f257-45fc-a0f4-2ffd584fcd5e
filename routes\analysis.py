#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
分析功能路由
处理学生详情分析、班级分析等功能
"""

from flask import Blueprint, render_template, request, jsonify, flash, redirect, url_for
from flask_login import login_required, current_user
from models.scholarship import ScholarshipData
from models.database import log_user_action
from services.ai_service import AIService

analysis_bp = Blueprint('analysis', __name__)

@analysis_bp.route('/student/<student_id>')
@login_required
def student_detail(student_id):
    """学生详情分析页面"""
    # 权限检查：学生只能查看自己的数据
    if current_user.is_student() and current_user.student_id != student_id:
        flash('您只能查看自己的数据', 'error')
        return redirect(url_for('dashboard.index'))
    
    scholarship_data = ScholarshipData()
    student_data = scholarship_data.get_student_detail(student_id)
    
    if not student_data:
        flash('未找到该学生的数据', 'error')
        return redirect(url_for('ranking.index'))
    
    return render_template('analysis/student_detail.html', 
                         student_data=student_data, 
                         student_id=student_id)

@analysis_bp.route('/api/student/<student_id>/trends')
@login_required
def api_student_trends(student_id):
    """获取学生成绩趋势数据"""
    # 权限检查
    if current_user.is_student() and current_user.student_id != student_id:
        return jsonify({'success': False, 'error': '权限不足'}), 403
    
    try:
        scholarship_data = ScholarshipData()
        student_data = scholarship_data.get_student_detail(student_id)
        
        if not student_data:
            return jsonify({'success': False, 'error': '未找到学生数据'}), 404
        
        # 整理趋势数据
        trends = []
        for record in student_data:
            trends.append({
                'semester': f"{record['academic_year']}-{record['semester']}",
                'academic_year': record['academic_year'],
                'semester_num': record['semester'],
                'total_score': record['total_score'],
                'academic_score': record['academic_score'],
                'comprehensive_score': record['comprehensive_score'],
                'total_rank': record['total_rank'],
                'academic_rank': record['academic_rank']
            })
        
        # 按学期排序
        trends.sort(key=lambda x: (x['academic_year'], x['semester_num']))
        
        return jsonify({
            'success': True,
            'data': trends
        })
        
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@analysis_bp.route('/api/student/<student_id>/ai_analysis', methods=['POST'])
@login_required
def api_student_ai_analysis(student_id):
    """AI智能分析学生情况"""
    # 权限检查
    if current_user.is_student() and current_user.student_id != student_id:
        return jsonify({'success': False, 'error': '权限不足'}), 403
    
    try:
        scholarship_data = ScholarshipData()
        student_data = scholarship_data.get_student_detail(student_id)
        
        if not student_data:
            return jsonify({'success': False, 'error': '未找到学生数据'}), 404
        
        # 调用AI服务进行分析
        ai_service = AIService()
        analysis_result = ai_service.analyze_student_performance(student_data)
        
        # 记录AI分析日志
        log_user_action(
            current_user.id,
            'ai_analysis',
            f'AI分析学生 {student_id} 的学业情况',
            request.remote_addr,
            request.headers.get('User-Agent')
        )
        
        return jsonify({
            'success': True,
            'data': {
                'analysis': analysis_result,
                'student_id': student_id,
                'generated_at': 'now'
            }
        })
        
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@analysis_bp.route('/class')
@login_required
def class_analysis():
    """班级分析页面"""
    # 学生用户不能访问班级分析
    if current_user.is_student():
        flash('您没有权限访问班级分析', 'error')
        return redirect(url_for('dashboard.index'))
    
    scholarship_data = ScholarshipData()
    filters = scholarship_data.get_available_filters()
    
    return render_template('analysis/class_analysis.html', filters=filters)

@analysis_bp.route('/api/class/data')
@login_required
def api_class_data():
    """获取班级分析数据"""
    # 权限检查
    if current_user.is_student():
        return jsonify({'success': False, 'error': '权限不足'}), 403

    try:
        academic_year = request.args.get('academic_year')
        semester = request.args.get('semester', type=int)
        major = request.args.get('major')
        grade = request.args.get('grade')  # 不强制转换为int，让后端处理

        # 记录请求参数用于调试
        print(f"班级数据API请求参数: academic_year={academic_year}, semester={semester}, major={major}, grade={grade}")

        if not academic_year or semester is None:
            return jsonify({
                'success': False,
                'error': '请选择学年和学期'
            }), 400

        # 处理年级参数
        grade_param = None
        if grade and grade.strip():  # 确保grade不是空字符串
            try:
                grade_param = int(grade)
            except ValueError:
                return jsonify({
                    'success': False,
                    'error': f'年级参数格式错误: {grade}'
                }), 400

        scholarship_data = ScholarshipData()
        class_data = scholarship_data.get_class_analysis_data(
            academic_year=academic_year,
            semester=semester,
            major=major,
            grade=grade_param
        )

        print(f"班级数据API返回: {len(class_data)} 个班级")

        return jsonify({
            'success': True,
            'data': class_data
        })

    except Exception as e:
        print(f"班级数据API错误: {e}")
        import traceback
        traceback.print_exc()
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@analysis_bp.route('/api/class/trend')
@login_required
def api_class_trend():
    """获取班级历年趋势数据"""
    # 权限检查
    if current_user.is_student():
        return jsonify({'success': False, 'error': '权限不足'}), 403

    try:
        class_name = request.args.get('class_name')
        major = request.args.get('major')
        grade = request.args.get('grade', type=int)

        if not class_name:
            return jsonify({
                'success': False,
                'error': '请指定班级名称'
            }), 400

        scholarship_data = ScholarshipData()
        trend_data = scholarship_data.get_class_trend_data(
            class_name=class_name,
            major=major,
            grade=grade
        )

        return jsonify({
            'success': True,
            'data': trend_data
        })

    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@analysis_bp.route('/api/class/ai_analysis', methods=['POST'])
@login_required
def api_class_ai_analysis():
    """AI智能分析班级情况"""
    # 权限检查
    if current_user.is_student():
        return jsonify({'success': False, 'error': '权限不足'}), 403
    
    try:
        data = request.get_json()
        academic_year = data.get('academic_year')
        semester = data.get('semester')
        major = data.get('major')
        class_name = data.get('class_name')
        
        if not all([academic_year, semester, class_name]):
            return jsonify({
                'success': False,
                'error': '缺少必要参数'
            }), 400
        
        scholarship_data = ScholarshipData()
        
        # 获取班级详细数据
        class_detail_data = scholarship_data.get_ranking_data(
            academic_year=academic_year,
            semester=semester,
            major=major
        )
        
        # 筛选指定班级的数据
        class_students = [
            student for student in class_detail_data 
            if student.get('class_name') == class_name
        ]
        
        if not class_students:
            return jsonify({
                'success': False,
                'error': '未找到班级数据'
            }), 404
        
        # 调用AI服务进行班级分析
        ai_service = AIService()
        analysis_result = ai_service.analyze_class_performance(class_students, class_name)
        
        # 记录AI分析日志
        log_user_action(
            current_user.id,
            'ai_class_analysis',
            f'AI分析班级 {class_name} 在 {academic_year}-{semester} 的情况',
            request.remote_addr,
            request.headers.get('User-Agent')
        )
        
        return jsonify({
            'success': True,
            'data': {
                'analysis': analysis_result,
                'class_name': class_name,
                'academic_year': academic_year,
                'semester': semester,
                'student_count': len(class_students),
                'generated_at': 'now'
            }
        })
        
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@analysis_bp.route('/api/grade_distribution')
@login_required
def api_grade_distribution():
    """获取成绩分布数据"""
    try:
        academic_year = request.args.get('academic_year')
        semester = request.args.get('semester', type=int)
        major = request.args.get('major')
        
        if not academic_year or semester is None:
            return jsonify({
                'success': False,
                'error': '请选择学年和学期'
            }), 400
        
        scholarship_data = ScholarshipData()
        distribution_data = scholarship_data.get_grade_distribution(
            academic_year=academic_year,
            semester=semester,
            major=major
        )
        
        return jsonify({
            'success': True,
            'data': distribution_data
        })
        
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500
