#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
班级分析功能测试脚本
"""

import requests
import json

BASE_URL = 'http://127.0.0.1:5000'

# 创建会话以保持登录状态
session = requests.Session()

def login_as_admin():
    """以管理员身份登录"""
    try:
        # 先获取登录页面
        response = session.get(f'{BASE_URL}/auth/login')
        if response.status_code != 200:
            print(f"❌ 无法访问登录页面: {response.status_code}")
            return False

        # 尝试登录（使用默认管理员账户）
        login_data = {
            'username': 'admin',
            'password': 'admin123'
        }

        response = session.post(f'{BASE_URL}/auth/login', data=login_data)

        # 检查是否登录成功（重定向到首页）
        if response.status_code == 302 or response.url.endswith('/'):
            print("✅ 管理员登录成功")
            return True
        else:
            print(f"❌ 登录失败: {response.status_code}")
            return False

    except Exception as e:
        print(f"❌ 登录异常: {e}")
        return False

def test_class_analysis_page():
    """测试班级分析页面是否可以访问"""
    try:
        response = requests.get(f'{BASE_URL}/analysis/class')
        print(f"班级分析页面访问测试: {response.status_code}")
        if response.status_code == 200:
            print("✅ 班级分析页面可以正常访问")
        else:
            print(f"❌ 班级分析页面访问失败: {response.status_code}")
        return response.status_code == 200
    except Exception as e:
        print(f"❌ 班级分析页面访问异常: {e}")
        return False

def test_class_data_api():
    """测试班级数据API"""
    try:
        # 测试参数
        params = {
            'academic_year': '2024-2025',
            'semester': 1
        }

        response = session.get(f'{BASE_URL}/analysis/api/class/data', params=params)
        print(f"班级数据API测试: {response.status_code}")
        print(f"响应内容: {response.text[:200]}...")

        if response.status_code == 200:
            try:
                data = response.json()
                if data.get('success'):
                    class_count = len(data.get('data', []))
                    print(f"✅ 班级数据API正常，返回 {class_count} 个班级")

                    # 显示前3个班级的信息
                    for i, cls in enumerate(data['data'][:3]):
                        print(f"  班级 {i+1}: {cls['class_name']} - 平均分: {cls['avg_total_score']:.2f}")

                    return True
                else:
                    print(f"❌ 班级数据API返回错误: {data.get('error')}")
                    return False
            except json.JSONDecodeError as e:
                print(f"❌ JSON解析失败: {e}")
                return False
        elif response.status_code == 302:
            print("❌ 需要登录才能访问API")
            return False
        else:
            print(f"❌ 班级数据API请求失败: {response.status_code}")
            return False

    except Exception as e:
        print(f"❌ 班级数据API测试异常: {e}")
        return False

def test_bug_reproduction():
    """重现用户报告的bug"""
    print("\n🐛 重现bug测试")
    print("-" * 30)

    try:
        # 步骤1: 测试正常查询（物联网专业）
        print("步骤1: 测试正常查询（物联网专业）")
        params1 = {
            'academic_year': '2024-2025',
            'semester': 1,
            'major': '物联网'
        }

        response1 = session.get(f'{BASE_URL}/analysis/api/class/data', params=params1)
        print(f"  响应状态: {response1.status_code}")

        if response1.status_code == 200:
            data1 = response1.json()
            if data1.get('success'):
                class_count1 = len(data1.get('data', []))
                print(f"  ✅ 返回 {class_count1} 个班级")
                if class_count1 > 0:
                    print(f"  示例班级: {data1['data'][0]['class_name']}")
            else:
                print(f"  ❌ 错误: {data1.get('error')}")

        # 步骤2: 添加年级筛选条件（grade=2024）
        print("\n步骤2: 添加年级筛选条件（grade=2024）")
        params2 = {
            'academic_year': '2024-2025',
            'semester': 1,
            'major': '物联网',
            'grade': 2024
        }

        response2 = session.get(f'{BASE_URL}/analysis/api/class/data', params=params2)
        print(f"  响应状态: {response2.status_code}")

        if response2.status_code == 200:
            data2 = response2.json()
            if data2.get('success'):
                class_count2 = len(data2.get('data', []))
                print(f"  返回 {class_count2} 个班级")
                if class_count2 == 0:
                    print("  ⚠️  未找到符合条件的班级数据（这是预期的bug）")
            else:
                print(f"  ❌ 错误: {data2.get('error')}")

        # 步骤3: 移除年级筛选条件，回到原始查询
        print("\n步骤3: 移除年级筛选条件，回到原始查询")
        params3 = {
            'academic_year': '2024-2025',
            'semester': 1,
            'major': '物联网'
        }

        response3 = session.get(f'{BASE_URL}/analysis/api/class/data', params=params3)
        print(f"  响应状态: {response3.status_code}")

        if response3.status_code == 200:
            data3 = response3.json()
            if data3.get('success'):
                class_count3 = len(data3.get('data', []))
                print(f"  返回 {class_count3} 个班级")
                if class_count3 == 0:
                    print("  ❌ Bug确认：移除筛选条件后仍然无法查询到数据")
                    return False
                else:
                    print("  ✅ 查询恢复正常")
                    return True
            else:
                print(f"  ❌ 错误: {data3.get('error')}")
                return False

        return True

    except Exception as e:
        print(f"❌ Bug重现测试异常: {e}")
        return False

def test_filters_api():
    """测试筛选条件API"""
    try:
        response = requests.get(f'{BASE_URL}/api/filters')
        print(f"筛选条件API测试: {response.status_code}")

        if response.status_code == 200:
            try:
                data = response.json()
                if data.get('success'):
                    filters = data.get('data', {})
                    majors_count = len(filters.get('majors', []))
                    semesters_count = len(filters.get('semesters', []))
                    print(f"✅ 筛选条件API正常，专业数: {majors_count}, 学期数: {semesters_count}")

                    # 显示专业信息
                    if majors_count > 0:
                        print("  专业列表:")
                        for major in filters['majors'][:3]:
                            if isinstance(major, dict):
                                print(f"    - {major.get('major_name', 'N/A')}")
                            else:
                                print(f"    - {major}")

                    return True
                else:
                    print(f"❌ 筛选条件API返回错误: {data.get('error')}")
                    return False
            except json.JSONDecodeError as e:
                print(f"❌ JSON解析失败: {e}")
                return False
        elif response.status_code == 302:
            print("❌ 需要登录才能访问API")
            return False
        else:
            print(f"❌ 筛选条件API请求失败: {response.status_code}")
            return False

    except Exception as e:
        print(f"❌ 筛选条件API测试异常: {e}")
        return False

def test_ai_analysis_api():
    """测试AI分析API（需要登录，这里只测试接口是否存在）"""
    try:
        # 测试数据
        test_data = {
            'academic_year': '2024-2025',
            'semester': 1,
            'class_name': '计工5班'
        }
        
        response = requests.post(
            f'{BASE_URL}/analysis/api/class/ai_analysis',
            json=test_data,
            headers={'Content-Type': 'application/json'}
        )
        
        print(f"AI分析API测试: {response.status_code}")
        
        # 由于需要登录，403是预期的响应
        if response.status_code in [401, 403]:
            print("✅ AI分析API接口存在（需要登录权限）")
            return True
        elif response.status_code == 200:
            print("✅ AI分析API接口正常工作")
            return True
        else:
            print(f"❌ AI分析API异常状态: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ AI分析API测试异常: {e}")
        return False

def main():
    """主测试函数"""
    print("=" * 50)
    print("班级分析功能测试")
    print("=" * 50)

    # 先登录
    if not login_as_admin():
        print("❌ 登录失败，无法继续测试")
        return

    tests = [
        ("班级分析页面访问", test_class_analysis_page),
        ("筛选条件API", test_filters_api),
        ("班级数据API", test_class_data_api),
        ("Bug重现测试", test_bug_reproduction),
        ("AI分析API", test_ai_analysis_api)
    ]

    results = []
    for test_name, test_func in tests:
        print(f"\n🧪 测试: {test_name}")
        print("-" * 30)
        result = test_func()
        results.append((test_name, result))

    print("\n" + "=" * 50)
    print("测试结果汇总")
    print("=" * 50)

    passed = 0
    for test_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{test_name}: {status}")
        if result:
            passed += 1

    print(f"\n总计: {passed}/{len(results)} 个测试通过")

    if passed == len(results):
        print("🎉 所有测试通过！班级分析功能实现成功！")
    else:
        print("⚠️  部分测试失败，请检查相关功能")

if __name__ == '__main__':
    main()
