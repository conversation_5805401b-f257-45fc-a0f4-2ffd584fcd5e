#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试Web API接口
"""

import requests
import json

BASE_URL = 'http://127.0.0.1:5000'

def test_api_without_login():
    """测试不登录时的API响应"""
    print("=" * 50)
    print("测试API接口（不登录）")
    print("=" * 50)
    
    try:
        # 测试班级数据API
        params = {
            'academic_year': '2024-2025',
            'semester': 1,
            'major': '物联网'
        }
        
        response = requests.get(f'{BASE_URL}/analysis/api/class/data', params=params)
        print(f"请求URL: {response.url}")
        print(f"响应状态: {response.status_code}")
        
        if response.status_code == 302:
            print("✅ 正确重定向到登录页面（需要登录）")
            return True
        elif response.status_code == 200:
            print("⚠️  API没有要求登录")
            return True
        else:
            print(f"❌ 意外的响应状态: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ 测试异常: {e}")
        return False

def login_and_get_session():
    """登录并获取会话"""
    session = requests.Session()
    
    try:
        # 登录
        login_data = {
            'username': 'admin',
            'password': 'admin123'
        }
        
        response = session.post(f'{BASE_URL}/auth/login', data=login_data)
        
        if response.status_code == 302:
            print("✅ 登录成功")
            return session
        else:
            print(f"❌ 登录失败: {response.status_code}")
            return None
            
    except Exception as e:
        print(f"❌ 登录异常: {e}")
        return None

def test_api_with_login():
    """测试登录后的API"""
    print("\n" + "=" * 50)
    print("测试API接口（已登录）")
    print("=" * 50)
    
    session = login_and_get_session()
    if not session:
        return False
    
    try:
        # 测试1: 不带年级筛选
        print("\n测试1: 不带年级筛选")
        print("-" * 30)
        params1 = {
            'academic_year': '2024-2025',
            'semester': 1,
            'major': '物联网'
        }
        
        response1 = session.get(f'{BASE_URL}/analysis/api/class/data', params=params1)
        print(f"请求URL: {response1.url}")
        print(f"响应状态: {response1.status_code}")
        
        if response1.status_code == 200:
            data1 = response1.json()
            if data1.get('success'):
                class_count1 = len(data1.get('data', []))
                print(f"✅ 返回 {class_count1} 个班级")
                for i, cls in enumerate(data1['data'][:3]):
                    print(f"  班级 {i+1}: {cls['class_name']} (年级: {cls['grade']}) - {cls['student_count']}人")
            else:
                print(f"❌ API错误: {data1.get('error')}")
                return False
        else:
            print(f"❌ 请求失败: {response1.status_code}")
            return False
        
        # 测试2: 带年级筛选 (2024)
        print("\n测试2: 带年级筛选 (2024)")
        print("-" * 30)
        params2 = {
            'academic_year': '2024-2025',
            'semester': 1,
            'major': '物联网',
            'grade': 2024
        }
        
        response2 = session.get(f'{BASE_URL}/analysis/api/class/data', params=params2)
        print(f"请求URL: {response2.url}")
        print(f"响应状态: {response2.status_code}")
        
        if response2.status_code == 200:
            data2 = response2.json()
            if data2.get('success'):
                class_count2 = len(data2.get('data', []))
                print(f"返回 {class_count2} 个班级")
                for i, cls in enumerate(data2['data'][:3]):
                    print(f"  班级 {i+1}: {cls['class_name']} (年级: {cls['grade']}) - {cls['student_count']}人")
            else:
                print(f"❌ API错误: {data2.get('error')}")
                return False
        else:
            print(f"❌ 请求失败: {response2.status_code}")
            return False
        
        # 测试3: 再次不带年级筛选
        print("\n测试3: 再次不带年级筛选")
        print("-" * 30)
        params3 = {
            'academic_year': '2024-2025',
            'semester': 1,
            'major': '物联网'
        }
        
        response3 = session.get(f'{BASE_URL}/analysis/api/class/data', params=params3)
        print(f"请求URL: {response3.url}")
        print(f"响应状态: {response3.status_code}")
        
        if response3.status_code == 200:
            data3 = response3.json()
            if data3.get('success'):
                class_count3 = len(data3.get('data', []))
                print(f"返回 {class_count3} 个班级")
                for i, cls in enumerate(data3['data'][:3]):
                    print(f"  班级 {i+1}: {cls['class_name']} (年级: {cls['grade']}) - {cls['student_count']}人")
                
                # 检查是否存在状态污染
                if class_count1 == class_count3:
                    print("\n✅ Web API没有状态污染问题")
                else:
                    print(f"\n❌ Web API存在状态污染: 第一次{class_count1}个班级，第三次{class_count3}个班级")
                
            else:
                print(f"❌ API错误: {data3.get('error')}")
                return False
        else:
            print(f"❌ 请求失败: {response3.status_code}")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ 测试异常: {e}")
        return False

def main():
    """主函数"""
    tests = [
        ("API接口（不登录）", test_api_without_login),
        ("API接口（已登录）", test_api_with_login)
    ]
    
    results = []
    for test_name, test_func in tests:
        print(f"\n🧪 {test_name}")
        result = test_func()
        results.append((test_name, result))
    
    print("\n" + "=" * 50)
    print("测试结果汇总")
    print("=" * 50)
    
    passed = 0
    for test_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\n总计: {passed}/{len(results)} 个测试通过")

if __name__ == '__main__':
    main()
